
// File upload limits
export const FILE_UPLOAD_LIMITS = {
  INVOICE: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ["application/pdf"],
    ALLOWED_EXTENSIONS: [".pdf"]
  },
  BANNER_IMAGE: {
    MAX_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_TYPES: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
    ALLOWED_EXTENSIONS: [".jpg", ".jpeg", ".png", ".webp"]
  },
  PRODUCT_IMAGE: {
    MAX_SIZE: 8 * 1024 * 1024, // 8MB
    ALLOWED_TYPES: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
    ALLOWED_EXTENSIONS: [".jpg", ".jpeg", ".png", ".webp"]
  }
} ;

// Network paths
export const NETWORK_PATHS = {
  INVOICE_STORAGE: process.env.INVOICE_STORAGE || "",
  PICTURES_STORAGE: process.env.PICTURES_BASE_PATH || ""
} as const;
