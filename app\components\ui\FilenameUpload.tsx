"use client";

import { useState, useRef } from "react";
import { Upload, Image as ImageIcon, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { FILE_UPLOAD_LIMITS } from "@/app/config/constants";
import Image from "next/image";

interface FilenameUploadProps {
  value?: string;
  onChange: (value: string) => void;
  label?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export default function FilenameUpload({
  value,
  onChange,
  label,
  description,
  required = false,
  disabled = false,
  placeholder = "Enter filename (e.g., my-banner.jpg)"
}: FilenameUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [filename, setFilename] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_TYPES.includes(file.type)) {
      return `Only image files are allowed (${FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS.join(', ')})`;
    }

    // Check file size
    if (file.size > FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE) {
      return `File size must be less than ${FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE / (1024 * 1024)}MB`;
    }

    return null;
  };

  const uploadFile = async (file: File, targetFilename: string) => {
    const validationError = validateFile(file);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    if (!targetFilename || targetFilename.trim().length === 0) {
      toast.error("Please enter a filename before uploading");
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("filename", targetFilename.trim());

      const response = await fetch("/api/banner/upload-image", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        onChange(result.data.filename);
        toast.success(result.message);
      } else {
        toast.error(result.error || "Failed to upload image");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("An unexpected error occurred while uploading the image");
    } finally {
      setIsUploading(false);
      setFilename("");
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // If no filename is entered, suggest one based on the uploaded file
    const targetFilename = filename.trim() || file.name;
    setFilename(targetFilename);
    
    uploadFile(file, targetFilename);
  };

  const handleUploadClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFilenameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilename = e.target.value;
    setFilename(newFilename);
    onChange(newFilename);
  };

  return (
    <div className="space-y-4">
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      {/* Filename Input */}
      <div className="flex gap-2">
        <Input
          value={value || ""}
          onChange={handleFilenameChange}
          placeholder={placeholder}
          disabled={disabled}
          className="flex-1"
        />
        <Button
          type="button"
          variant="outline"
          onClick={handleUploadClick}
          disabled={disabled || isUploading}
          className="shrink-0"
        >
          {isUploading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Upload className="h-4 w-4" />
          )}
          {isUploading ? "Uploading..." : "Upload"}
        </Button>
      </div>

      {/* Upload Instructions */}
      <Card className="border-dashed">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <ImageIcon className="h-4 w-4" />
            <span>
              Enter a filename above, then click Upload to select and upload an image file
            </span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            Supported: {FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS.join(', ')} 
            (max {FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE / (1024 * 1024)}MB)
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      {value && (
        <Card>
          <CardContent className="p-4">
            <div className="text-sm font-medium mb-2">Preview:</div>
            <Image
              src={`/api/images/${value}`}
              alt="Banner preview"
              className="w-full max-w-md h-auto rounded border"
              width={800}
              height={600}
            />
          </CardContent>
        </Card>
      )}

      {description && (
        <p className="text-sm text-gray-600">{description}</p>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept={FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS.join(',')}
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
}
