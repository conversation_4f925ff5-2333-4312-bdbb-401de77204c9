"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import logger, { logError, logInfo } from "@/lib/logger";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnAction } from "./actions";
import { NETWORK_PATHS, FILE_UPLOAD_LIMITS } from "@/app/config/constants";
import fs from "fs";
import path from "path";

export async function uploadInvoiceAction(formData: FormData): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email;

  try {
    // Extract data from FormData
    const file = formData.get("file") as File;
    const orderId = formData.get("orderId") as string;

    // Validate inputs
    if (!file) {
      logError(`uploadInvoiceAction -> No file provided by ${userEmail}`);
      return {
        status: "ERROR",
        message: "No file selected. Please choose a PDF file to upload.",
      };
    }

    if (!orderId) {
      logError(`uploadInvoiceAction -> No orderId provided by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Order ID is required.",
      };
    }

    // Validate file type
    if (!FILE_UPLOAD_LIMITS.INVOICE.ALLOWED_TYPES.includes(file.type)) {
      logError(`uploadInvoiceAction -> Invalid file type: ${file.type} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Only PDF files are allowed.",
      };
    }

    // Validate file size
    if (file.size > FILE_UPLOAD_LIMITS.INVOICE.MAX_SIZE) {
      logError(`uploadInvoiceAction -> File too large: ${file.size} bytes by ${userEmail}`);
      return {
        status: "ERROR",
        message: "File size must be less than 10MB.",
      };
    }

    // Get order details
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: { 
        id: true, 
        orderNumber: true,
        createdAt: true,
        invoiceAM: true
      }
    });

     if (!order) {
      logError(`uploadInvoiceAction -> Order not found: ${orderId} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Order not found.",
      };
    }

    //extract from file.name the name without the extension
    const fileNameWithoutExtension = path.parse(file.name).name;

    if (order.invoiceAM !== fileNameWithoutExtension) {
      logError(`uploadInvoiceAction -> Invoice number does not match: ${order?.invoiceAM} !== ${fileNameWithoutExtension} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Invoice number and the file name does not match.",
      };
    }

    // Create filename with timestamp
    // const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    // const filename = `invoice_${order.orderNumber}_${timestamp}.pdf`;
    
    // // Create year/month subdirectory structure
    // const uploadDate = new Date();
    // const year = uploadDate.getFullYear().toString();
    // const month = (uploadDate.getMonth() + 1).toString().padStart(2, "0");
    
    // //const targetDir = path.join(NETWORK_PATHS.INVOICE_STORAGE, year, month);
    // const targetDir = path.join(NETWORK_PATHS.INVOICE_STORAGE, year, month);
    // const targetPath = path.join(targetDir, filename);

    // // Ensure directory exists
    // try {
    //   await fs.promises.mkdir(targetDir, { recursive: true });
    // } catch (dirError) {
    //   logError(`uploadInvoiceAction -> Failed to create directory ${targetDir}: ${dirError} by ${userEmail}`);
    //   return {
    //     status: "ERROR",
    //     message: "Failed to create upload directory. Please check network access.",
    //   };
    // }

    const originalFilename = path.basename(file.name);

    // Create the target path directly in INVOICE_STORAGE
    const targetPath = path.join(NETWORK_PATHS.INVOICE_STORAGE, originalFilename);

    // Check if file already exists (optional protection)
    try {
      await fs.promises.access(targetPath);
      logger.info(`uploadInvoiceAction -> File already exists: ${originalFilename}, will overwrite by ${userEmail}`);
    } catch {
      // File doesn't exist, which is good for new uploads
    }

    // Convert file to buffer and save
    try {
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      await fs.promises.writeFile(targetPath, buffer);
      
      logInfo(`uploadInvoiceAction -> Successfully uploaded invoice for order ${order.orderNumber} to ${targetPath} by ${userEmail}`);
      
      // Optionally update the order record with the file path
      await prisma.order.update({
        where: { id: orderId },
        data: { 
          invoiceFilePath: targetPath 
        }
      });

      revalidatePath(`/orders/${orderId}`);
      
      return {
        status: "SUCCESS",
        message: `Invoice uploaded successfully for order ${order.orderNumber}`,
      };

    } catch (fileError) {
      logError(`uploadInvoiceAction -> Failed to save file: ${fileError} by ${userEmail}`);
      return {
        status: "ERROR",
        message: "Failed to save file. Please check network connectivity and permissions.",
      };
    }

  } catch (error) {
    logError(`uploadInvoiceAction -> Unexpected error: ${error} by ${userEmail}`);
    return {
      status: "ERROR",
      message: "An unexpected error occurred while uploading the invoice.",
    };
  }
}
