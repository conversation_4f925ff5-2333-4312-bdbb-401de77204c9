import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { FILE_UPLOAD_LIMITS, NETWORK_PATHS } from "@/app/config/constants";
import { logError, logInfo } from "@/lib/logger";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export async function POST(request: NextRequest) {
  try {
    // Authenticate and authorize user
    const user = await requireAdminOrModerator();
    
    logInfo(`API uploadBannerImage -> Upload request initiated by ${user.email}`);

    // Parse form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const filename = formData.get("filename") as string; // User-specified filename

    // Validate inputs
    if (!file) {
      logError(`API uploadBannerImage -> No file provided by ${user.email}`);
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    if (!filename || typeof filename !== 'string' || filename.trim().length === 0) {
      logError(`API uploadBannerImage -> No filename provided by ${user.email}`);
      return NextResponse.json(
        { error: "Filename is required" },
        { status: 400 }
      );
    }

    // Sanitize filename - remove path separators and dangerous characters
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-_]/g, '_').trim();
    if (sanitizedFilename.length === 0) {
      logError(`API uploadBannerImage -> Invalid filename after sanitization: ${filename} by ${user.email}`);
      return NextResponse.json(
        { error: "Invalid filename provided" },
        { status: 400 }
      );
    }

    // Validate file type
    if (!FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_TYPES.includes(file.type)) {
      logError(`API uploadBannerImage -> SECURITY: Invalid file type attempted`, {
        user: user.email,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json(
        {
          error: `Invalid file type. Only image files are allowed (${FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS.join(', ')})`,
          code: 'INVALID_FILE_TYPE',
          allowedTypes: FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS
        },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE) {
      logError(`API uploadBannerImage -> SECURITY: File size limit exceeded`, {
        user: user.email,
        fileName: file.name,
        fileSize: file.size,
        maxSize: FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json(
        {
          error: `File size exceeds limit. Maximum allowed size is ${FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE / (1024 * 1024)}MB`,
          code: 'FILE_TOO_LARGE',
          maxSize: FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE,
          currentSize: file.size
        },
        { status: 400 }
      );
    }

    // Ensure the pictures directory exists
    const picturesDir = NETWORK_PATHS.PICTURES_STORAGE;

    try {
      await fs.promises.access(picturesDir);
    } catch {
      logError(`API uploadBannerImage -> Pictures directory does not exist: ${picturesDir}`);
      return NextResponse.json(
        { error: "Storage directory not accessible" },
        { status: 500 }
      );
    }

    // Use the sanitized filename directly (no subdirectory, matching product pattern)
    const targetPath = path.join(picturesDir, sanitizedFilename);

    // Check if file already exists and warn user
    try {
      await fs.promises.access(targetPath);
      logInfo(`API uploadBannerImage -> File already exists, will be overwritten: ${targetPath} by ${user.email}`);
    } catch {
      // File doesn't exist, which is fine
    }

    // Convert file to buffer and save
    try {
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Get file stats for audit logging
      const uploadStartTime = Date.now();

      await fs.promises.writeFile(targetPath, buffer);

      // Get file stats after upload
      //const fileStats = await fs.promises.stat(targetPath);
      const uploadDuration = Date.now() - uploadStartTime;

      // Enhanced audit logging
      logInfo(`API uploadBannerImage -> AUDIT: Banner image uploaded successfully by ${user.email} as ${sanitizedFilename}`);

      return NextResponse.json({
        success: true,
        message: `Banner image uploaded successfully as ${sanitizedFilename}`,
        data: {
          filename: sanitizedFilename,
          originalName: file.name,
          size: file.size,
          type: file.type,
          uploadDuration,
          dimensions: await getImageDimensions(buffer, file.type)
        }
      });

    } catch (error) {
      logError(`API uploadBannerImage -> Failed to save file: ${error} by ${user.email}`);
      return NextResponse.json(
        { error: "Failed to save file" },
        { status: 500 }
      );
    }

  } catch (error) {
    logError(`API uploadBannerImage -> Unexpected error:`, error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}

// Helper function to extract basic image dimensions
async function getImageDimensions(buffer: Buffer, mimeType: string): Promise<{ width?: number; height?: number } | null> {
  try {
    // Basic image dimension extraction for common formats
    if (mimeType === 'image/png') {
      // PNG format: width and height are at bytes 16-19 and 20-23
      if (buffer.length >= 24) {
        const width = buffer.readUInt32BE(16);
        const height = buffer.readUInt32BE(20);
        return { width, height };
      }
    } else if (mimeType === 'image/jpeg' || mimeType === 'image/jpg') {
      // JPEG format: more complex, but we can try to find SOF markers
      // This is a simplified version - for production, consider using a proper image library
      for (let i = 0; i < buffer.length - 8; i++) {
        if (buffer[i] === 0xFF && (buffer[i + 1] === 0xC0 || buffer[i + 1] === 0xC2)) {
          const height = buffer.readUInt16BE(i + 5);
          const width = buffer.readUInt16BE(i + 7);
          return { width, height };
        }
      }
    }
    return null;
  } catch (error) {
    logError('Error extracting image dimensions:', error);
    return null;
  }
}
