"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { logError, logInfo } from "@/lib/logger";
import { 
  UpdateUserFormValues, 
  ChangeUserStatusFormValues,
  updateUserSchema,
  changeUserStatusSchema,
  AddUserToGroupFormValues,
  RemoveUserFromGroupFormValues,
  removeUserFromGroupSchema,
  addUserToGroupSchema
} from "@/app/zod/userSchemas";
import { redirect } from "next/navigation";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnAction } from "./actions";
import { clerkClient } from "@clerk/nextjs/server";
import { z } from "zod";
import { Prisma } from "@/generated/prisma";
type UserPreferences = {
  newsletterOptIn: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  emailNotifications: boolean;
};
export async function updateUser(data: UpdateUserFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  if (!actor || (actor.role !== 'administAB' && actor.role !== 'moderatorAB') || !actor.isActive || actor.isSuspended) {
    redirect("/unauthorized");
  }

  try {
    // Validate input data
    const validatedData = updateUserSchema.parse(data);
    const { id, newpassword, confirmpassword, ...updateData } = validatedData;

    // Check if user exists in database
    const existingUser = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        externalId: true,
        email: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        userAM: true,
        role: true,
        isActive: true,
        isSuspended: true,
        suspensionReason: true,
        inactiveReason: true,
        newsletterOptIn: true,
        smsNotifications: true,
        pushNotifications: true,
        emailNotifications: true,
        twoFactorEnabled: true,
        passwordEnabled: true,
      }
    });
    
    if (!existingUser || !existingUser.externalId) {
      logError(`User not found or missing Clerk ID: ${id} by ${actorEmail}`);
      return {
        status: "ERROR",
        message: "User not found",
      };
    }

    // Validate password if provided
    if (newpassword || confirmpassword) {
      if (!existingUser.passwordEnabled) {
        return {
          status: "ERROR",
          message: "Password cannot be set for this user",
        };
      }
      if (newpassword !== confirmpassword) {
        return {
          status: "ERROR",
          message: "Passwords do not match",
        };
      }
      if (newpassword && newpassword.length < 8) {
        return {
          status: "ERROR",
          message: "Password must be at least 8 characters long",
        };
      }
    }

    // Track changes for audit log
    const changes: Record<string, { from: unknown; to: unknown }> = {};
    
    // Prepare Clerk update data
    const clerkUser = await clerkClient()
    const clerkUpdateData: Parameters<typeof clerkUser.users.updateUser>[1] = {};
    
    // Handle email update (set as primary email address in Clerk)
    if (updateData.email && updateData.email !== existingUser.email) {
      try {

        await clerkUser.emailAddresses.createEmailAddress({
          userId: existingUser.externalId,
          emailAddress: updateData.email,
          verified: true,
          primary: true
        });

        // Get the user object which contains emailAddresses
        const user = await clerkUser.users.getUser(existingUser.externalId);

        // Access email addresses through user.emailAddresses
        const emailAddresses = user.emailAddresses;
        
        // Delete all other email addresses
        for (const emailAddr of emailAddresses) {
          if (emailAddr.emailAddress !== updateData.email) {
            await clerkUser.emailAddresses.deleteEmailAddress(emailAddr.id);
          }
        }


        
        changes.email = { from: existingUser.email, to: updateData.email };
      } catch (emailError) {
        logError(`[updateUser]Failed to update email in Clerk: ${emailError} for user ${id} by ${actorEmail}`);
        return {
          status: "ERROR",
          message: "Failed to update email address",
        };
      }
    }
    
    // Handle phone number update (set as email in Clerk)
    if (updateData.phoneNumber !== existingUser.phoneNumber && updateData.phoneNumber) {
      try {

        const user = await clerkUser.users.getUser(existingUser.externalId);

        // Access email addresses through user.emailAddresses
        const phoneNumbers = user.phoneNumbers;

        // Delete all other phone numbers
        for (const phone of phoneNumbers) {
          if (phone.phoneNumber !== updateData.phoneNumber) {
            await clerkUser.phoneNumbers.deletePhoneNumber(phone.id);
          }
        }

        // Create phone number as email address in Clerk
        await clerkUser.phoneNumbers.createPhoneNumber({
          userId: existingUser.externalId,
          phoneNumber: updateData.phoneNumber,
          verified: true,
          primary: false
        });
        
        changes.phoneNumber = { from: existingUser.phoneNumber, to: updateData.phoneNumber };
      } catch (phoneError) {
        logError(`Failed to add phone as email in Clerk: ${phoneError} for user ${id} by ${actorEmail}`);
        return {
          status: "ERROR",
          message: "Failed to update phone number",
        };
      }
    }
    
    // Update basic user info
    if (updateData.firstName !== existingUser.firstName) {
      clerkUpdateData.firstName = updateData.firstName;
      changes.firstName = { from: existingUser.firstName, to: updateData.firstName };
    }
    if (updateData.lastName !== existingUser.lastName) {
      clerkUpdateData.lastName = updateData.lastName;
      changes.lastName = { from: existingUser.lastName, to: updateData.lastName };
    }
    
    // Handle password update
    if (newpassword) {
      clerkUpdateData.password = newpassword;
      changes.password = { from: "[REDACTED]", to: "[REDACTED]" };
    }
    
    // Handle two-factor authentication (can only disable, not enable)
    if (updateData.twoFactorEnabled === false && existingUser.twoFactorEnabled === true) {
      try {
        // Disable all two-factor methods
        const user = await clerkUser.users.getUser(existingUser.externalId);
        if (user.twoFactorEnabled) {
          await clerkUser.users.disableUserMFA(existingUser.externalId);
        }
        changes.twoFactorEnabled = { from: true, to: false };
      } catch (mfaError) {
        logError(`Failed to disable 2FA: ${mfaError} for user ${id} by ${actorEmail}`);
        return {
          status: "ERROR",
          message: "Failed to disable Two Factor Authentication",
        };
      }
    }
    
    // Prepare private metadata
    const privateMetadata: Record<string, UserPreferences> = {};
    
    // Update preferences in private metadata
    const currentPreferences: UserPreferences = {
      newsletterOptIn: existingUser.newsletterOptIn,
      smsNotifications: existingUser.smsNotifications,
      pushNotifications: existingUser.pushNotifications,
      emailNotifications: existingUser.emailNotifications,
    };
    
    const newPreferences: UserPreferences = {
      newsletterOptIn: updateData.newsletterOptIn ?? existingUser.newsletterOptIn,
      smsNotifications: updateData.smsNotifications ?? existingUser.smsNotifications,
      pushNotifications: updateData.pushNotifications ?? existingUser.pushNotifications,
      emailNotifications: updateData.emailNotifications ?? existingUser.emailNotifications,
    };
    
    if (JSON.stringify(currentPreferences) !== JSON.stringify(newPreferences)) {
      privateMetadata.preferences = newPreferences;
      changes.preferences = { from: currentPreferences, to: newPreferences };
    }
    
    // Add metadata to Clerk update if changed
    if (Object.keys(privateMetadata).length > 0) {
      clerkUpdateData.privateMetadata = privateMetadata;
    }
    
    // Update user in Clerk if there are changes
    if (Object.keys(clerkUpdateData).length > 0) {
      try {
        await clerkUser.users.updateUser(existingUser.externalId, clerkUpdateData);
      } catch (clerkError) {
        logError(`Clerk API error: ${clerkError} for user ${id} by ${actorEmail}`);
        return {
          status: "ERROR",
          message: "Failed to update user in authentication system",
        };
      }
    }
    
    // Handle user ban status (isActive mapped negatively to ban)
    if (updateData.isActive !== undefined && updateData.isActive !== existingUser.isActive) {
      try {
        if (!updateData.isActive) {
          // User is inactive, so ban in Clerk
          await clerkUser.users.banUser(existingUser.externalId);
        } else {
          // User is active, so unban in Clerk
          await clerkUser.users.unbanUser(existingUser.externalId);
        }
        changes.isActive = { from: existingUser.isActive, to: updateData.isActive };
      } catch (banError) {
        logError(`Failed to update ban status: ${banError} for user ${id} by ${actorEmail}`);
        return {
          status: "ERROR",
          message: "Failed to update user active status",
        };
      }
    }
    
    // Handle user lock status (isSuspended mapped to locked)
    if (updateData.isSuspended !== undefined && updateData.isSuspended !== existingUser.isSuspended) {
      try {
        if (updateData.isSuspended) {
          // User is suspended, so lock in Clerk
          await clerkUser.users.lockUser(existingUser.externalId);
        } else {
          // User is not suspended, so unlock in Clerk
          await clerkUser.users.unlockUser(existingUser.externalId);
        }
        changes.isSuspended = { from: existingUser.isSuspended, to: updateData.isSuspended };
      } catch (lockError) {
        logError(`Failed to update lock status: ${lockError} for user ${id} by ${actorEmail}`);
        return {
          status: "ERROR",
          message: "Failed to update user suspension status",
        };
      }
    }
    
    // Update user in database with transaction
    const user = await prisma.$transaction(async (tx) => {
      // Track database-only field changes
      if (updateData.userAM !== undefined && updateData.userAM !== existingUser.userAM) {
        changes.userAM = { from: existingUser.userAM, to: updateData.userAM };
      }
      if (updateData.suspensionReason !== undefined && updateData.suspensionReason !== existingUser.suspensionReason) {
        changes.suspensionReason = { from: existingUser.suspensionReason, to: updateData.suspensionReason };
      }
      if (updateData.inactiveReason !== undefined && updateData.inactiveReason !== existingUser.inactiveReason) {
        changes.inactiveReason = { from: existingUser.inactiveReason, to: updateData.inactiveReason };
      }
      if (updateData.role !== undefined && updateData.role !== existingUser.role) {
        changes.role = { from: existingUser.role, to: updateData.role };
      }
      
      // Prepare database update data
      const dbUpdateData: Prisma.UserUpdateInput = {
        //email: updateData.email,
        //firstName: updateData.firstName,
        //lastName: updateData.lastName,
        //phoneNumber: updateData.phoneNumber,
        userAM: updateData.userAM,
        role: updateData.role,
        //isActive: updateData.isActive,
        //isSuspended: updateData.isSuspended,
        //suspensionReason: updateData.suspensionReason,
        //inactiveReason: updateData.inactiveReason,
        newsletterOptIn: updateData.newsletterOptIn,
        smsNotifications: updateData.smsNotifications,
        pushNotifications: updateData.pushNotifications,
        emailNotifications: updateData.emailNotifications,
        twoFactorEnabled: updateData.twoFactorEnabled === false ? false : existingUser.twoFactorEnabled,
        updatedBy: actorEmail,
        updatedAt: new Date(),
      };
      
      // Remove undefined values
      Object.keys(dbUpdateData).forEach(key => {
        if (dbUpdateData[key as keyof typeof dbUpdateData] === undefined) {
          delete dbUpdateData[key as keyof typeof dbUpdateData];
        }
      });
      
      // Update the user in database
      const updatedUser = await tx.user.update({
        where: { id },
        data: dbUpdateData
      });
      
      // Create audit log entry if there were changes
      if (Object.keys(changes).length > 0) {
        await tx.userAuditLog.create({
          data: {
            entityType: "user",
            entityId: updatedUser.id,
            userId: updatedUser.id,
            action: "USER_UPDATED",
            details: `User ${actorEmail} updated: ${JSON.stringify(changes)}`,
            ipAddress: "server-side",
            userAgent: "server-side",
            performedBy: actorEmail
          }
        });
      }
      
      return updatedUser;
    });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    revalidatePath(`/users/${id}`);
    
    logInfo(`User updated successfully: ${user.email} by ${actorEmail} with changes: ${JSON.stringify(changes)}`);
    return {
      status: "SUCCESS",
      message: "User updated successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      logError(`Validation error updating user: ${error.message} by ${actorEmail}`);
      return {
        status: "ERROR",
        message: "Invalid data provided",
      };
    }
    
    logError(`Error updating user: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to update user",
    };
  }
}

// export async function updateUser(data: UpdateUserFormValues): Promise<ReturnAction>{
//   const actor = await requireAdminOrModerator();
//   const actorEmail = actor.email

//   if (!actor || (actor.role !== 'administAB' && actor.role !== 'moderatorAB') || !actor.isActive || actor.isSuspended) {
//     redirect("/unauthorized");
//   }

//   try {
//     // Validate input data
//     const validatedData = updateUserSchema.parse(data);
//     const { id, ...updateData } = validatedData;

//     // Check if user exists
//     const existingUser = await prisma.user.findUnique({
//       where: { id }
//     });
    
//     if (!existingUser) {
//       logError(`User not found: ${id} by ${actorEmail}`)
//       return {
//         status: "ERROR",
//         message: "User not found",
//       };
//     }
    
//     // Track changes for audit log
//     const changes: Record<string, { from: any; to: any }> = {};

//     // Update user in database
//     const user = await prisma.$transaction(async (tx) => {
      
//       // Compare old and new values
//       Object.keys(updateData).forEach(key => {
//         const typedKey = key as keyof typeof updateData;
//         if (updateData[typedKey] !== existingUser[typedKey as keyof typeof existingUser]) {
//           changes[key] = {
//             from: existingUser[typedKey as keyof typeof existingUser],
//             to: updateData[typedKey]
//           };
//         }
//       });
      
//       // Update the user
//       const updatedUser = await tx.user.update({
//         where: { id },
//         data: {
//           ...updateData,
//           updatedBy: actorEmail,
//           updatedAt: new Date()
//         }
//       });
      
//       // Create audit log entry if there were changes
//       if (Object.keys(changes).length > 0) {
//         await tx.userAuditLog.create({
//           data: {
//             entityType: "user",
//             entityId: updatedUser.id,
//             userId: updatedUser.id,
//             action: "USER_UPDATED",
//             details: `User ${actorEmail} updated: ${JSON.stringify(changes)}`,
//             ipAddress: "server-side",
//             userAgent: "server-side",
//             performedBy: actorEmail
//           }
//         });
//       }
      
//       return updatedUser;
//     });
    
//     // Revalidate relevant paths
//     revalidatePath("/users");
//     revalidatePath(`/users/${id}`);
    
//     logInfo(`User updated successfully: ${user.email} by ${actorEmail} with changes: ${JSON.stringify(changes)}`);
//     return {
//       status: "SUCCESS",
//       message: "User updated successfully",
//     };
//   } catch (error) {
//     logError(`Error updating user: ${error} by ${actorEmail}`);
//     return {
//       status: "ERROR",
//       message: "Failed to update user",
//     };
//   }
// }

// export async function updateUser(data: UpdateUserFormValues): Promise<ReturnAction> {
//   const actor = await requireAdminOrModerator();
//   const actorEmail = actor.email;

//   if (!actor || (actor.role !== 'administAB' && actor.role !== 'moderatorAB') || !actor.isActive || actor.isSuspended) {
//     redirect("/unauthorized");
//   }

//   try {
//     // Validate input data
//     const validatedData = updateUserSchema.parse(data);
//     const { id, newpassword, confirmpassword, ...updateData } = validatedData;

//     // Check if user exists in database
//     const existingUser = await prisma.user.findUnique({
//       where: { id }
//     });
    
//     if (!existingUser) {
//       logError(`User not found: ${id} by ${actorEmail}`);
//       return {
//         status: "ERROR",
//         message: "User not found",
//       };
//     }

//     // Validate password if provided
//     if (newpassword || confirmpassword) {
//       if (newpassword !== confirmpassword) {
//         return {
//           status: "ERROR",
//           message: "Passwords do not match",
//         };
//       }
//       if (newpassword && newpassword.length < 12) {
//         return {
//           status: "ERROR",
//           message: "Password must be at least 12 characters long",
//         };
//       }
//     }

//     // Track changes for audit log
//     const changes: Record<string, { from: any; to: any }> = {};
    
//     // Prepare Clerk update data
//     const client = await clerkClient();
//     const clerkUpdateData: Parameters<typeof client.users.updateUser>[1] = {};
    
//     // Map fields to Clerk API
//     if (updateData.firstName !== existingUser.firstName) {
//       clerkUpdateData.firstName = updateData.firstName;
//     }
//     if (updateData.lastName !== existingUser.lastName) {
//       clerkUpdateData.lastName = updateData.lastName;
//     }
//     if (updateData.profileImage !== existingUser.profileImage) {
//       //clerkUpdateData.profileImageUrl = updateData.profileImage || undefined;
//     }
    
//     // Handle password update
//     if (newpassword) {
//       clerkUpdateData.password = newpassword;
//       changes.password = { from: "[REDACTED]", to: "[REDACTED]" };
//     }
    
//     // Update user metadata in Clerk
//     const publicMetadata: Record<string, any> = {};
//     const privateMetadata: Record<string, any> = {};
    
//     // Public metadata (visible to frontend)
//     if (updateData.role !== existingUser.role) {
//       publicMetadata.role = updateData.role;
//     }
//     if (updateData.isActive !== existingUser.isActive) {
//       publicMetadata.isActive = updateData.isActive;
//     }
//     if (updateData.isSuspended !== existingUser.isSuspended) {
//       publicMetadata.isSuspended = updateData.isSuspended;
//     }
    
//     // Private metadata (backend only)
//     if (updateData.phoneNumber !== existingUser.phoneNumber) {
//       privateMetadata.phoneNumber = updateData.phoneNumber;
//     }
//     if (updateData.userAM !== existingUser.userAM) {
//       privateMetadata.userAM = updateData.userAM;
//     }
//     if (updateData.suspensionReason !== existingUser.suspensionReason) {
//       privateMetadata.suspensionReason = updateData.suspensionReason;
//     }
//     if (updateData.inactiveReason !== existingUser.inactiveReason) {
//       privateMetadata.inactiveReason = updateData.inactiveReason;
//     }
    
//     // Preferences in private metadata
//     const preferencesChanged = 
//       updateData.newsletterOptIn !== existingUser.newsletterOptIn ||
//       updateData.smsNotifications !== existingUser.smsNotifications ||
//       updateData.pushNotifications !== existingUser.pushNotifications ||
//       updateData.emailNotifications !== existingUser.emailNotifications;
    
//     if (preferencesChanged) {
//       privateMetadata.preferences = {
//         newsletterOptIn: updateData.newsletterOptIn,
//         smsNotifications: updateData.smsNotifications,
//         pushNotifications: updateData.pushNotifications,
//         emailNotifications: updateData.emailNotifications,
//       };
//     }
    
//     // Add metadata to Clerk update if changed
//     if (Object.keys(publicMetadata).length > 0) {
//       clerkUpdateData.publicMetadata = publicMetadata;
//     }
//     if (Object.keys(privateMetadata).length > 0) {
//       clerkUpdateData.privateMetadata = privateMetadata;
//     }
    
//     // Handle user suspension/ban in Clerk
//     if (updateData.isSuspended !== existingUser.isSuspended) {
//       if (updateData.isSuspended) {
//         await clerkClient.users.banUser(existingUser.clerkId);
//       } else {
//         await clerkClient.users.unbanUser(existingUser.clerkId);
//       }
//     }
    
//     // Update user in Clerk if there are changes
//     let clerkUser;
//     if (Object.keys(clerkUpdateData).length > 0 || updateData.isSuspended !== existingUser.isSuspended) {
//       try {
//         clerkUser = await clerkClient.users.updateUser(existingUser.clerkId, clerkUpdateData);
//       } catch (clerkError) {
//         logError(`Clerk API error: ${clerkError} for user ${id} by ${actorEmail}`);
//         return {
//           status: "ERROR",
//           message: "Failed to update user in authentication system",
//         };
//       }
//     }
    
//     // Update user in database with transaction
//     const user = await prisma.$transaction(async (tx) => {
//       // Compare old and new values for audit log
//       Object.keys(updateData).forEach(key => {
//         const typedKey = key as keyof typeof updateData;
//         if (updateData[typedKey] !== existingUser[typedKey as keyof typeof existingUser]) {
//           changes[key] = {
//             from: existingUser[typedKey as keyof typeof existingUser],
//             to: updateData[typedKey]
//           };
//         }
//       });
      
//       // Prepare database update data
//       const dbUpdateData: Prisma.UserUpdateInput = {
//         ...updateData,
//         updatedBy: actorEmail,
//         updatedAt: new Date(),
//       };
      
//       // Remove fields that shouldn't be in database
//       delete (dbUpdateData as any).newpassword;
//       delete (dbUpdateData as any).confirmpassword;
      
//       // Update the user in database
//       const updatedUser = await tx.user.update({
//         where: { id },
//         data: dbUpdateData
//       });
      
//       // Create audit log entry if there were changes
//       if (Object.keys(changes).length > 0) {
//         await tx.userAuditLog.create({
//           data: {
//             entityType: "user",
//             entityId: updatedUser.id,
//             userId: updatedUser.id,
//             action: "USER_UPDATED",
//             details: `User ${actorEmail} updated: ${JSON.stringify(changes)}`,
//             ipAddress: "server-side",
//             userAgent: "server-side",
//             performedBy: actorEmail
//           }
//         });
//       }
      
//       return updatedUser;
//     });
    
//     // Revalidate relevant paths
//     revalidatePath("/users");
//     revalidatePath(`/users/${id}`);
    
//     logInfo(`User updated successfully: ${user.email} by ${actorEmail} with changes: ${JSON.stringify(changes)}`);
//     return {
//       status: "SUCCESS",
//       message: "User updated successfully",
//     };
//   } catch (error) {
//     if (error instanceof z.ZodError) {
//       logError(`Validation error updating user: ${error.message} by ${actorEmail}`);
//       return {
//         status: "ERROR",
//         message: "Invalid data provided",
//       };
//     }
    
//     logError(`Error updating user: ${error} by ${actorEmail}`);
//     return {
//       status: "ERROR",
//       message: "Failed to update user",
//     };
//   }
// }

// export async function changeUserPassword(data: ChangeUserPasswordFormValues) {
//   const actor = await requireAdminOrModerator();
//   const actorEmail = actor.email
//   try {
//     // Validate input data
//     const validatedData = changeUserPasswordSchema.parse(data);
//     const { userId, newPassword } = validatedData;
    
//     // In a real app, you would hash the password before storing it
//     // For Clerk integration, use their API to reset the password
    
//     // Update audit log
//     await prisma.userAuditLog.create({
//       data: {
//         userId,
//         entityType: "user",
//         entityId: userId,
//         action: "PASSWORD_CHANGED",
//         details: "Password was changed",
//         ipAddress: "server-side",
//         userAgent: "server-side",
//         performedBy: actorEmail
//       }
//     });
    
//     // Revalidate relevant paths
//     revalidatePath(`/users/${userId}`);
    
//     logInfo(`Password changed for user ${userId} by ${actorEmail}`);
    
//     return { success: true };
//   } catch (error) {
//     logError(`Error changing user password: ${error} by ${actorEmail}`);
//     return { success: false };
//   }
// }

export async function changeUserStatus(data: ChangeUserStatusFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email
  try {
    // Validate input data
    const validatedData = changeUserStatusSchema.parse(data);
    const { userId, isActive, isSuspended, suspensionReason } = validatedData;
    
    // Get current user status
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { isActive: true, isSuspended: true, suspensionReason: true }
    });
    
    if (!user) {
        logError(`User not found: ${userId} by ${actorEmail}`)
      return {
        status: "ERROR",
        message: "User not found",
      };
    }
    
    // Update user status
     await prisma.$transaction(async (tx) => {
      // Update the user
      const updated = await tx.user.update({
        where: { id: userId },
        data: {
          isActive,
          isSuspended,
          suspensionReason: isSuspended ? suspensionReason : null,
          updatedBy: actorEmail,
          updatedAt: new Date()
        }
      });
      
      // Determine the action type
      let action = "USER_STATUS_CHANGED";
      if (isActive && !user.isActive) action = "USER_ACTIVATED";
      if (!isActive && user.isActive) action = "USER_DEACTIVATED";
      if (isSuspended && !user.isSuspended) action = "USER_SUSPENDED";
      if (!isSuspended && user.isSuspended) action = "USER_UNSUSPENDED";
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          userId,
          entityType: "user",
          entityId: userId,
          action,
          details: isSuspended && suspensionReason 
            ? `Status changed. Suspension reason: ${suspensionReason}`
            : "User status changed",
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
      
      return updated;
    });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    revalidatePath(`/users/${userId}`);
    
    logInfo(`User status changed for user ${userId} with changes: ${JSON.stringify({ isActive, isSuspended, suspensionReason })} by ${actorEmail}`);
    
    return {
      status: "SUCCESS",
      message: "User status changed successfully",
    };
  } catch (error) {
    logError(`Error changing user status: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to change user status",
    };
  }
}

export async function deleteUser(userId: string) {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email

  if(!userId) return { success: false };

  try {
    
    // Soft delete the user
    // const user = await prisma.$transaction(async (tx) => {
    //   // Update the user
    //   const deletedUser = await tx.user.update({
    //     where: { id: userId },
    //     data: {
    //       deletedAt: new Date(),
    //       updatedBy: actorEmail,
    //       updatedAt: new Date()
    //     }
    //   });
      
    //   // Create audit log entry
    //   await tx.userAuditLog.create({
    //     data: {
    //       userId,
    //       entityType: "user",
    //       entityId: userId,
    //       action: "USER_DELETED",
    //       details: "User was soft-deleted",
    //       ipAddress: "server-side",
    //       userAgent: "server-side",
    //       performedBy: actorEmail
    //     }
    //   });
      
    //   return deletedUser;
    // });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    
    //logInfo(`User deleted: ${user.email} by ${actorEmail}`);
    
    return { success: true };
  } catch (error) {
    logError(`Error deleting user: ${error} by ${actorEmail}`);
    return { success: false };
  }
}

export async function restoreUser(userId: string) {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email

  try {
    
    // Restore the soft-deleted user
    const user = await prisma.$transaction(async (tx) => {
      // Update the user
      const restoredUser = await tx.user.update({
        where: { id: userId },
        data: {
          deletedAt: null,
          updatedBy: actorEmail,
          updatedAt: new Date()
        }
      });
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          entityType: "user",
          entityId: userId,
          userId,
          action: "USER_RESTORED",
          details: "User was restored from deleted state",
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
      
      return restoredUser;
    });
    
    // Revalidate relevant paths
    revalidatePath("/users");
    revalidatePath(`/users/${userId}`);
    
    logInfo(`User restored: ${user.email} by ${actorEmail}`);
    
    return { success: true };
  } catch (error) {
    logError(`Error restoring user: ${error} by ${actorEmail}`);
    return { success: false };
  }
}

export async function addUserToGroup(data: AddUserToGroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email
  try {
    // Validate input data
    const validatedData = addUserToGroupSchema.parse(data);
    const { userId, groupId } = validatedData;
    
    // Check if user is already in the group
    const existingMembership = await prisma.userGroup.findFirst({
      where: {
        id: groupId,
        users: {
          some: {
            id: userId
          }
        }
      }
    });
    
    if (existingMembership) {
      logError(`User is already a member of this group: ${userId} in ${groupId} by ${actorEmail}`)
      return {
        status: "ERROR",
        message: "User is already a member of this group",
      };
    }
    
    // Add user to group
    await prisma.$transaction(async (tx) => {
      // Update the user-group relationship
      await tx.userGroup.update({
        where: { id: groupId },
        data: {
          users: {
            connect: { id: userId }
          }
        }
      });
      
      // Get group name for the audit log
      const group = await tx.userGroup.findUnique({
        where: { id: groupId },
        select: { name: true }
      });
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          userId,
          entityType: "user",
          entityId: userId,
          action: "USER_ADDED_TO_GROUP",
          details: `User added to group: ${group?.name || groupId}`,
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
    });
    
    // Revalidate relevant paths
    revalidatePath(`/users/${userId}`);
    revalidatePath(`/groups/${groupId}`);
    
    logInfo(`User ${userId} added to group ${groupId} by ${actorEmail}`);
    
    return {
      status: "SUCCESS",
      message: "User added to group successfully",
    };
  } catch (error) {
    logError(`Error adding user to group: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to add user to group",
    };
  }
}

export async function removeUserFromGroup(data: RemoveUserFromGroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email
  try {
    // Validate input data
    const validatedData = removeUserFromGroupSchema.parse(data);
    const { userId, groupId } = validatedData;
    
    // Remove user from group
    await prisma.$transaction(async (tx) => {
      // Get group name for the audit log
      const group = await tx.userGroup.findUnique({
        where: { id: groupId },
        select: { name: true }
      });
      
      // Update the user-group relationship
      await tx.userGroup.update({
        where: { id: groupId },
        data: {
          users: {
            disconnect: { id: userId }
          }
        }
      });
      
      // Create audit log entry
      await tx.userAuditLog.create({
        data: {
          entityType: "user",
          entityId: userId,
          userId,
          action: "USER_REMOVED_FROM_GROUP",
          details: `User removed from group: ${group?.name || groupId}`,
          ipAddress: "server-side",
          userAgent: "server-side",
          performedBy: actorEmail
        }
      });
    });
    
    // Revalidate relevant paths
    revalidatePath(`/users/${userId}`);
    revalidatePath(`/groups/${groupId}`);
    
    logInfo(`User ${userId} removed from group ${groupId} by ${actorEmail}`);
    
    return {
      status: "SUCCESS",
      message: "User removed from group successfully",
    };
  } catch (error) {
    logError(`Error removing user from group: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to remove user from group",
    };
  }
}

export async function recordUserLogin(userId: string, ipAddress?: string, userAgent?: string) {
  try {
    // Update user login information
    await prisma.user.update({
      where: { id: userId },
      data: {
        lastLoginAt: new Date(),
        loginCount: { increment: 1 },
        lastActivityAt: new Date()
      }
    });
    
    // Create audit log entry
    await prisma.userAuditLog.create({
      data: {
        entityType: "user",
        entityId: userId,
        userId,
        action: "USER_LOGIN",
        details: "User logged in",
        ipAddress: ipAddress || "unknown",
        userAgent: userAgent || "unknown",
        performedBy: userId // Self-performed action
      }
    });
    
    logInfo(`User login recorded: ${userId} from ${ipAddress}`);
    
    return { success: true };
  } catch (error) {
    logError("Error recording user login:", { error, userId });
    // Don't throw here - login should succeed even if recording fails
    return { success: false };
  }
}

export async function syncUsers(): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    // Get all users from Clerk
    const clerk = await clerkClient();
    const clerkUsers = await clerk.users.getUserList({
      limit: 100, // Adjust as needed
    });
    
    // Process each Clerk user
    let syncCount = 0;
    let newCount = 0;
    let updatedCount = 0;
    
    for (const clerkUser of clerkUsers.data) {
      // Skip users without email
      const primaryEmail = clerkUser.emailAddresses.find(
        email => email.id === clerkUser.primaryEmailAddressId
      );
      
      if (!primaryEmail?.emailAddress) continue;
      
      // Check if user already exists in our database
      const existingUser = await prisma.user.findFirst({
        where: { externalId: clerkUser.id }
      });
      
      // Prepare user data from Clerk
      const userData = {
        email: primaryEmail.emailAddress,
        firstName: clerkUser.firstName || "Unknown",
        lastName: clerkUser.lastName || "Unknown",
        profileImage: clerkUser.imageUrl || "",
        externalId: clerkUser.id,
        externalProvider: "clerk",
        emailVerified: primaryEmail.verification?.status === "verified" 
          ? new Date() 
          : null,
        isActive: !clerkUser.banned,
        lastLoginAt: clerkUser.lastSignInAt ? new Date(clerkUser.lastSignInAt) : null,
        updatedBy: actorEmail,
        updatedAt: new Date()
      };
      
      if (existingUser) {
        // Update existing user
        await prisma.user.update({
          where: { id: existingUser.id },
          data: userData
        });
        updatedCount++;
      } else {
        // Create new user with default role
        await prisma.user.create({
          data: {
            ...userData,
            role: "inregistratAB", // Default role for new users
            profileImage: clerkUser.imageUrl || "https://ui-avatars.com/api/?name=" + 
              encodeURIComponent(`${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`),
          }
        });
        newCount++;
      }
      
      syncCount++;
    }
    
    // Log the sync operation
    logInfo(`User sync completed by ${actorEmail}: ${syncCount} users processed, ${newCount} new, ${updatedCount} updated`);
    
    // Revalidate relevant paths
    revalidatePath("/users");
    
    return {
      status: "SUCCESS",
      message: `Users synchronized successfully: ${syncCount} users processed, ${newCount} new, ${updatedCount} updated`,
    };
  } catch (error) {
    logError(`Error syncing users: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to synchronize users",
    };
  }
}


// export async function createUser(data: CreateUserFormValues) {
//   try {
//     // Validate input data
//     const validatedData = createUserSchema.parse(data);
//     const actorEmail = await getCurrentUserEmail();
    
//     // Check if user with this email already exists
//     const existingUser = await prisma.user.findUnique({
//       where: { email: validatedData.email }
//     });
    
//     if (existingUser) {
//       throw new Error("A user with this email already exists");
//     }
    
//     // Create user in database
//     const user = await prisma.$transaction(async (tx) => {
//       // Create the user
//       const newUser = await tx.user.create({
//         data: {
//           ...validatedData,
//           createdBy: actorEmail,
//           updatedBy: actorEmail,
//         }
//       });
      
//       // Create audit log entry
//       await tx.userauditlog.create({
//         data: {
//           userId: newUser.id,
//           action: "USER_CREATED",
//           details: `User created with email ${newUser.email}`,
//           ipAddress: "server-side", // In a real app, you'd capture the client IP
//           userAgent: "server-side", // In a real app, you'd capture the client user agent
//           performedBy: actorEmail
//         }
//       });
      
//       return newUser;
//     });
    
//     // Revalidate relevant paths
//     revalidatePath("/users");
    
//     logInfo(`User created successfully: ${user.email}`, { 
//       userId: user.id, 
//       actorEmail 
//     });
    
//     return user;
//   } catch (error) {
//     logError("Error creating user:", { error, data });
//     throw error;
//   }
// }
