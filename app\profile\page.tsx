import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Edit, Mail,  Calendar } from "lucide-react";
import Link from "next/link";
import { requireAdminOrModerator } from "@/lib/auth-utils";

export default async function ProfilePage() {
  const user = await requireAdminOrModerator();
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">My Profile</h1>
          <p className="text-gray-500 mt-1">Manage your account information</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex flex-col items-center w-full">
                  <Avatar className="h-24 w-24 mb-4">
                    <AvatarImage src={user.profileImage} alt={`${user.firstName} ${user.lastName}`} />
                    <AvatarFallback className="text-2xl">
                      {user.firstName?.charAt(0) || ""}{user.lastName?.charAt(0) || ""}
                    </AvatarFallback>
                  </Avatar>
                  <CardTitle className="text-xl text-center">{user.firstName} {user.lastName}</CardTitle>
                  <CardDescription className="text-center">{user.email}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mt-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{user.email}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>Joined {new Date(user.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
              
              <div className="mt-6">
                <Link href="https://accounts.yourdomain.com/user" target="_blank">
                  <Button variant="outline" className="w-full">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Profile
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Your account details and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium">Email Verification</h3>
                  <p className="text-sm text-gray-500">
                    {user.emailVerified ? "Your email is verified" : "Please verify your email address"}
                  </p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">Two-Factor Authentication</h3>
                  <p className="text-sm text-gray-500">
                    {user.twoFactorEnabled 
                      ? "Two-factor authentication is enabled" 
                      : "Two-factor authentication is not enabled"}
                  </p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">Account Type</h3>
                  <p className="text-sm text-gray-500">
                    {user.role}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}