"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { Upload, X, Image as ImageIcon, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { FILE_UPLOAD_LIMITS } from "@/app/config/constants";
import Image from "next/image";

interface ImageUploadProps {
  value?: string;
  onChange: (value: string) => void;
  imageType: 'desktop' | 'mobile';
  label?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function ImageUpload({
  value,
  onChange,
  imageType,
  label,
  description,
  required = false,
  disabled = false
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate preview URL for existing images
  const getImageUrl = useCallback((imagePath: string) => {
    if (!imagePath) return null;
    // If it's already a full URL, return as is
    if (imagePath.startsWith('http')) return imagePath;
    // Otherwise, construct the path to the network storage
    return `/api/images/${imagePath}`;
  }, []);

  // Set preview URL when value changes
  useEffect(() => {
    if (value) {
      setPreviewUrl(getImageUrl(value));
    } else {
      setPreviewUrl(null);
    }
  }, [value, getImageUrl]);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_TYPES.includes(file.type)) {
      return `Only image files are allowed (${FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS.join(', ')})`;
    }

    // Check file name for spaces
    if (file.name.includes(' ')) {
      return `File name cannot contain white spaces`;
    }

    // Check file size
    if (file.size > FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE) {
      return `File size must be less than ${FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE / (1024 * 1024)}MB`;
    }

    return null;
  };

  const uploadFile = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("imageType", imageType);

      const response = await fetch("/api/banner/upload-image", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        onChange(result.data.relativePath);
        setPreviewUrl(URL.createObjectURL(file));
        toast.success(result.message);
      } else {
        toast.error(result.error || "Failed to upload image");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("An unexpected error occurred while uploading the image");
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    uploadFile(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;
    
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  };

  const handleRemove = () => {
    onChange("");
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <Card className={`relative ${disabled ? 'opacity-50' : ''}`}>
        <CardContent className="p-0">
          {previewUrl ? (
            <div className="relative">
              <Image
                src={previewUrl}
                alt="Preview"
                className="w-full h-48 object-cover rounded-lg"
                width={imageType === 'desktop' ? 1920 : 800}
                height={imageType === 'desktop' ? 1200 : 600}
              />
              {!disabled && (
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={handleRemove}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              {isUploading && (
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
                  <Loader2 className="h-8 w-8 animate-spin text-white" />
                </div>
              )}
            </div>
          ) : (
            <div
              className={`
                border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                ${isDragOver ? 'border-primary bg-primary/5' : 'border-gray-300'}
                ${disabled ? 'cursor-not-allowed' : 'hover:border-primary hover:bg-primary/5'}
              `}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleClick}
            >
              {isUploading ? (
                <div className="flex flex-col items-center space-y-2">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <p className="text-sm text-gray-600">Uploading...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center space-y-2">
                  <div className="flex items-center space-x-2">
                    <Upload className="h-8 w-8 text-gray-400" />
                    <ImageIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">
                      Drop your image here, or <span className="text-primary">browse</span>
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS.join(', ')} up to {FILE_UPLOAD_LIMITS.BANNER_IMAGE.MAX_SIZE / (1024 * 1024)}MB
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {description && (
        <p className="text-sm text-gray-600">{description}</p>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept={FILE_UPLOAD_LIMITS.BANNER_IMAGE.ALLOWED_EXTENSIONS.join(',')}
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
}
