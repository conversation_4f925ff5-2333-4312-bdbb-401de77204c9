"use client";

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import Papa from 'papaparse';
import { uploadCategoriesCSVAction } from "@/app/actions/categoryActions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CategoryUploadData } from "@/app/types/Types";

export default function CategoryUploadCSV() {
  const [isPending, startTransition] = useTransition();
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    setFile(selectedFile || null);
  };

  const handleFileUpload = () => {
    if (!file) {
      toast.error("Please select a CSV file.");
      return;
    }

    Papa.parse(file, {
      header: true,
      delimiter: ",",
      skipEmptyLines: true,
      complete: async (results) => {
        const rows = results.data as Record<string, string>[];

        const parsedData = rows
          .map((row) => {
            const level1Name = row.level1Name?.trim();
            const level2Name = row.level2Name?.trim();
            const level3Name = row.level3Name?.trim();
            const familyCode = row.familyCode?.trim();

            if (!level1Name || !level2Name || !level3Name || !familyCode) return null;

            return {
              level1Name,
              level1NameRO: row.level1NameRO?.trim() || undefined,
              level1ImageUrl: row.level1ImageUrl?.trim() || undefined,
              level2Name,
              level2NameRO: row.level2NameRO?.trim() || undefined,
              level2ImageUrl: row.level2ImageUrl?.trim() || undefined,
              level3Name,
              level3NameRO: row.level3NameRO?.trim() || undefined,
              level3ImageUrl: row.level3ImageUrl?.trim() || undefined,
              familyCode
            };
          })
          .filter(Boolean) as CategoryUploadData[];

        if (parsedData.length === 0) {
          toast.error("CSV appears to be empty or malformed.");
          return;
        }

        startTransition(async () => {
          try {
            const response = await uploadCategoriesCSVAction(parsedData);

            if (response.status === "SUCCESS") {
              toast.success(response.message);
              // Reset file input
              setFile(null);
              if (document.getElementById('csv-file-input') as HTMLInputElement) {
                (document.getElementById('csv-file-input') as HTMLInputElement).value = '';
              }
            } else {
              toast.error(response.message);
            }
          } catch (error) {
            toast.error("An unexpected error occurred.");
            console.error(error);
          }
        });
      },
      error: (err) => {
        toast.error("Failed to parse CSV file.");
        console.error(err);
      },
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Categories CSV</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <p className="text-sm text-muted-foreground">
            Upload a CSV file with the following columns: level1Name, level1NameRO, level1ImageUrl, level2Name, level2NameRO, level2ImageUrl, level3Name, level3NameRO, level3ImageUrl, familyCode
          </p>
          <div className="flex gap-4 items-center">
            <input 
              id="csv-file-input"
              type="file" 
              accept=".csv" 
              onChange={handleFileChange} 
              className="max-w-sm"
            />
            <Button
              variant="default"
              onClick={handleFileUpload}
              disabled={isPending || !file}
            >
              {isPending ? "Processing..." : "Upload Categories"}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}