import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { SignedIn, SignedOut } from "@clerk/nextjs";
import { Package, ShoppingCart, Tag, Users } from "lucide-react";
import prisma from "./utils/db";
import ReturnsDashboard from "@/app/components/dashboard/ReturnsDashboard";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import OrderDashboard from "./components/dashboard/OrderDashboard";
import ServiceDashboard from "./components/dashboard/ServiceDashboard";
import { RecentTransactionsChart } from "./components/chart/RecentTransactionsChart";
import { formatDate } from "./utils/formatters";


//function that will return the count of products
const getProductCount = async () => {
  const count = await prisma.product.count();
  return count;
};

//function that will return the count of orders
const getOrderCount = async () => {
  const count = await prisma.order.count();
  return count;
};

const activeDiscounts = async () => {
  const count = await prisma.discount.count({
    where: {
      active: true,
    },
  });
  return count;
};

const activeUsers = async () => {
  const count = await prisma.user.count({
    where: {
      isActive: true,
      isSuspended: false,
    },
  });
  return count;
};

async function getData(): Promise<{ date: string; revenue: number }[]> {
  const now = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(now.getDate() - 7);

  const data = await prisma.order.findMany({
    where: {
      createdAt: {
        gte: sevenDaysAgo,
      },
    },
    select: {
      amount: true,
      createdAt: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  const result = data.map((item) => ({
    date: formatDate(item.createdAt),
    revenue: Number(item.amount),
  }));
  return result;
}

export default async function Home() {
  const user =await requireAdminOrModerator();
  const data = await getData();
  return (
    <div className="container mx-auto py-8">
      <SignedOut>
        <div className="flex flex-col items-center justify-center py-12">
          <h1 className="text-4xl font-bold text-center mb-4">Welcome to Parts Database</h1>
          <p className="text-xl text-center text-gray-600 mb-8 max-w-2xl">
            Manage your automotive parts inventory, track orders, and more with our comprehensive database system.
          </p>
          <div className="flex gap-4">
            <Button asChild size="lg">
              <Link href="/sign-up">Get Started</Link>
            </Button>
            <Button variant="outline" asChild size="lg">
              <Link href="/sign-in">Sign In</Link>
            </Button>
          </div>
        </div>
      </SignedOut>
      
      <SignedIn>
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-gray-500 mt-1">Welcome back, {user?.firstName}!</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{await getProductCount()}</div>
                <Package className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{await getOrderCount()}</div>
                <ShoppingCart className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Discounts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{await activeDiscounts()}</div>
                <Tag className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{await activeUsers()}</div>
                <Users className="h-5 w-5 text-gray-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="xl:col-span-2 mb-8">
          <CardHeader>
            <CardTitle>Transactions</CardTitle>
            <CardDescription>
              Recent transactions from the last 7 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentTransactionsChart data={data} />
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="col-span-1">
            <OrderDashboard />
          </div>

          <div className="col-span-1">
            <ServiceDashboard />
          </div>
        
          <div className="col-span-1">
            <ReturnsDashboard />
          </div>
          
        </div>
      </SignedIn>
    </div>
  );
}

