import { Metadata } from "next";
import OrderList from "../components/order/OrderList";
import { getOrders } from "../getData/order/getOrders";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnOrders } from "../types/order";

export const metadata: Metadata = {
  title: "Order Management",
  description: "View and manage customer orders",
};

export default async function OrdersRoute() {
  await requireAdminOrModerator();
  const orders: ReturnOrders[] = await getOrders();

  return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Order Management</h1>
        </div>

        <OrderList orders={orders} />
      </div>
  );
}
