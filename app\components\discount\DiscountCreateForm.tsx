"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { createDiscountSchema, DiscountFormValues } from "@/app/zod/zod";
import { useRouter } from "next/navigation";
import { ServerError } from "@/app/types/Types";

interface CreateFormProps {
  createAction: (data: DiscountFormValues) => Promise<{ status: string; message: string; fieldErrors?: Record<string, string> }>;
}

export default function CreateDiscountForm({ createAction }: CreateFormProps) {
  const [serverError, setServerError] = useState<ServerError | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    reset,
  } = useForm<DiscountFormValues>({
    resolver: zodResolver(createDiscountSchema),
  });

  const onSubmit = async (data: DiscountFormValues) => {
    setServerError(null); // Clear previous server error

    try {
      const response = await createAction(data);

      if(response.status === "SUCCESS"){
        router.push("/discounts");
        reset();
        toast.success(response.message);
      }else{
        toast.error(response.message)

        if (response.fieldErrors) {
          Object.entries(response.fieldErrors).forEach(([field, message]) => {
            setError(field as keyof DiscountFormValues, {
              type: "server",
              message,
            });
          });
        } else {
          setServerError({ message: String(response.message) });
        }
      }        
    } catch (error) {
      console.error("Unexpected error:", error);
      setServerError({ message: "Unexpected error occurred. Please try again later." });
    } 
  };

  return (

      <div className="mx-auto w-1/4 border-2 border-gray-100 p-4 rounded-lg">
        {serverError && (
          <p className="text-red-500 mb-2">{serverError.message}</p>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
            <label className="block mb-1">Name:</label>
            <input
                type="text"
                {...register("name")}
                className="w-full p-2 border rounded"
            />
            {errors.name && <p className="text-red-500">{errors.name.message}</p>}
            </div>

            <div>
            <label className="block mb-1">Description:</label>
            <input
                type="text"
                {...register("description")}
                className="w-full p-2 border rounded"
            />
            {errors.description && <p className="text-red-500">{errors.description.message}</p>}
            </div>

            <div>
            <label className="block mb-1">Type:</label>
            <select {...register("type")} className="w-full p-2 border rounded">
                <option value="PERCENTAGE">Percentage</option>
                <option value="FIXED_AMOUNT">Fixed Amount</option>
                <option value="NEW_PRICE">New Price</option>
            </select>
            {errors.type && <p className="text-red-500">{errors.type.message}</p>}
            </div>

            <div>
            <label className="block mb-1">Value:</label>
            <input
                type="number"
                step="0.01"
                {...register("value", { valueAsNumber: true })} // <--- Add { valueAsNumber: true }
                className="w-full p-2 border rounded"
            />
            {errors.value && <p className="text-red-500">{errors.value.message}</p>}
            </div>

            <div className="flex items-center gap-2">
            <label className="block mb-1">Active:</label>
            <input
                type="checkbox"
                {...register("active")}
                defaultChecked
            />
            {errors.active && <p className="text-red-500">{errors.active.message}</p>}
            </div>

            <div>
            <label className="block mb-1">Start Date:</label>
            <input
                type="datetime-local"
                {...register("startDate")}
                className="w-full p-2 border rounded"
            />
            {errors.startDate && (
                <p className="text-red-500">{errors.startDate.message}</p>
            )}
            </div>

            <div>
            <label className="block mb-1">End Date:</label>
            <input
                type="datetime-local"
                {...register("endDate")}
                className="w-full p-2 border rounded"
            />
            {errors.endDate && (
                <p className="text-red-500">{errors.endDate.message}</p>
            )}
            </div>

            <Button
              type="submit"
              disabled={isSubmitting}
              variant="default"        
              className="w-full"
            >
              {isSubmitting  ? "Creating..." : "Create Discount"}
            </Button>
        </form>
      </div>

  );
}