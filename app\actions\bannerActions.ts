"use server"


import { revalidatePath } from "next/cache";
import { CreateBannerFormValues, UpdateBannerFormValues, createBannerSchema, updateBannerSchema } from "../zod/zod";
import prisma from "../utils/db";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { BannerPlacement } from "@/generated/prisma";
import { logError, logInfo } from "@/lib/logger";
import { ReturnAction } from "./actions";
import fs from "fs";
import path from "path";
import { NETWORK_PATHS } from "@/app/config/constants";

export async function createBanner(data: CreateBannerFormValues) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const validatedData = createBannerSchema.parse(data);
    
    const banner = await prisma.banner.create({
      data: {
        ...validatedData,
        placement: validatedData.placement as BannerPlacement,
        createdBy: userEmail,
        updatedBy: userEmail,
      }
    });

    revalidatePath("/banner");

    // Enhanced audit logging for banner creation
    logInfo(`AUDIT: Banner created successfully by ${userEmail}. The banner id is ${banner.id} `);

    return { success: true, banner };
  } catch (error) {
    logError(`Error creating banner by ${userEmail}:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create banner" 
    };
  }
}

export async function updateBanner(data: UpdateBannerFormValues) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const validatedData = updateBannerSchema.parse(data);
    const { id, ...updateData } = validatedData;

    // Get the current banner to check for image changes
    const currentBanner = await prisma.banner.findUnique({
      where: { id },
      select: { imageUrl: true, mobileImageUrl: true }
    });

    const banner = await prisma.banner.update({
      where: { id },
      data: {
        ...updateData,
        placement: validatedData.placement as BannerPlacement,
        updatedBy: userEmail,
      }
    });

    // Clean up old images if they were changed
    if (currentBanner) {
      if (currentBanner.imageUrl !== validatedData.imageUrl && currentBanner.imageUrl && !currentBanner.imageUrl.startsWith('http')) {
        await cleanupImageFile(currentBanner.imageUrl, userEmail);
      }
      if (currentBanner.mobileImageUrl !== validatedData.mobileImageUrl && currentBanner.mobileImageUrl && !currentBanner.mobileImageUrl.startsWith('http')) {
        await cleanupImageFile(currentBanner.mobileImageUrl, userEmail);
      }
    }

    logInfo(`Banner updated successfully: ${banner.id} by ${userEmail}`);
    revalidatePath("/banner");
    revalidatePath(`/banner/${id}`);
    return { success: true, banner };
  } catch (error) {
    logError(`Error updating banner by ${userEmail}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update banner"
    };
  }
}

export async function deleteBanner(id: string): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    // Get banner data before deletion to clean up images
    const banner = await prisma.banner.findUnique({
      where: { id },
      select: { imageUrl: true, mobileImageUrl: true }
    });

    await prisma.banner.delete({
      where: { id },
    });

    // Clean up associated image files
    if (banner) {
      if (banner.imageUrl && !banner.imageUrl.startsWith('http')) {
        await cleanupImageFile(banner.imageUrl, userEmail);
      }
      if (banner.mobileImageUrl && !banner.mobileImageUrl.startsWith('http')) {
        await cleanupImageFile(banner.mobileImageUrl, userEmail);
      }
    }

    logInfo(`Banner deleted successfully: ${id} by ${userEmail}`);
    revalidatePath("/banner");
    return {
      status: "SUCCESS",
      message: "Banner deleted successfully",
    };
  } catch (error) {
    logError(`Error deleting banner by ${userEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to delete banner",
    };
  }
}

export async function incrementBannerImpressions(id: string) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    await prisma.banner.update({
      where: { id },
      data: {
        impressions: {
          increment: 1
        }
      }
    });
    logInfo(`Banner impression incremented: ${id} by ${userEmail}`);
    return { success: true };
  } catch (error) {
      logError(`Error incrementing banner impressions by ${userEmail}:`, error);
    return { success: false };
  }
}

export async function incrementBannerClicks(id: string) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const banner = await prisma.banner.update({
      where: { id },
      data: {
        clicks: {
          increment: 1
        }
      }
    });

    // Update conversion rate
    if (banner.impressions > 0) {
      const conversionRate = (banner.clicks / banner.impressions) * 100;
      await prisma.banner.update({
        where: { id },
        data: {
          conversionRate
        }
      });
    }

    logInfo(`Banner click incremented: ${id} by ${userEmail}`);
    return { success: true };
  } catch (error) {
    logError(`Error incrementing banner clicks by ${userEmail}:`, error);
    return { success: false };
  }
}

// Helper function to clean up image files
async function cleanupImageFile(filename: string, userEmail: string) {
  try {
    // Skip cleanup for URLs
    if (filename.startsWith('http')) {
      return;
    }

    const fullPath = path.join(NETWORK_PATHS.PICTURES_STORAGE, filename);

    // Check if file exists before attempting to delete
    try {
      await fs.promises.access(fullPath);
      await fs.promises.unlink(fullPath);
      logInfo(`Cleaned up banner image file: ${fullPath} by ${userEmail}`);
    } catch {
      // File doesn't exist or can't be accessed, which is fine
      logInfo(`Banner image file not found or already deleted: ${fullPath} by ${userEmail}`);
    }
  } catch (error) {
    logError(`Error cleaning up banner image file ${filename} by ${userEmail}:`, error);
  }
}
