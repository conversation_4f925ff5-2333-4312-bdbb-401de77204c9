import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import prisma from "@/app/utils/db";
import { ServiceStatus, ResolutionType } from "@/generated/prisma";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { sendServiceStatusChangeEmail, ServiceStatusChangeEmailData } from "@/lib/email";

const updateStatusSchema = z.object({
  status: z.nativeEnum(ServiceStatus),
  notes: z.string().optional(),
  resolution: z.nativeEnum(ResolutionType).optional(),
  resolutionNotes: z.string().optional(),
});

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> } 
) {
  try {
    // Check authentication and authorization
    const user = await requireAdminOrModerator();
    const { id } = await context.params;

    // Validate request body
    const body = await request.json();
    const validatedData = updateStatusSchema.parse(body);

    // Get the current service request
    const currentService = await prisma.serviceRequest.findUnique({
      where: { id },
      select: { status: true, resolution: true },
    });

    if (!currentService) {
      return NextResponse.json(
        { error: "Service request not found" },
        { status: 404 }
      );
    }

    // Update the service request
    const updatedService = await prisma.serviceRequest.update({
      where: { id },
      data: {
        status: validatedData.status,
        resolution: validatedData.resolution,
        resolutionNotes: validatedData.resolutionNotes,
        resolvedAt: validatedData.status === ServiceStatus.completed ? new Date() : undefined,
        updatedAt: new Date(),
      },
    });

    // Create status history entry
    await prisma.serviceStatusHistory.create({
      data: {
        serviceRequestId: id,
        previousStatus: currentService.status,
        newStatus: validatedData.status,
        notes: validatedData.notes,
        changedBy: user.id,
        changedAt: new Date(),
      },
    });

    // Send email notification to customer (async, don't wait for it)
    if (currentService.status !== validatedData.status) {
      // Get service details for email
      const serviceForEmail = await prisma.serviceRequest.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          },
          action: {
            include: {
              orderItem: {
                include: {
                  order: {
                    select: {
                      orderNumber: true
                    }
                  },
                  product: {
                    select: {
                      Description_Local: true,
                      Material_Number: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (serviceForEmail && serviceForEmail.user && serviceForEmail.action) {
        const emailData: ServiceStatusChangeEmailData = {
          serviceNumber: serviceForEmail.serviceNumber,
          customerName: `${serviceForEmail.user.firstName} ${serviceForEmail.user.lastName}`,
          customerEmail: serviceForEmail.user.email,
          newStatus: validatedData.status,
          previousStatus: currentService.status,
          productName: serviceForEmail.action.orderItem?.product.Description_Local || 'Unknown Product',
          productCode: serviceForEmail.action.orderItem?.product.Material_Number || 'Unknown Code',
          orderNumber: serviceForEmail.action.orderItem?.order?.orderNumber || 'Unknown Order',
          resolution: validatedData.resolution,
          resolutionNotes: validatedData.resolutionNotes,
          adminNotes: validatedData.notes
        };

        // Send email asynchronously (don't await to avoid blocking the response)
        sendServiceStatusChangeEmail(emailData).catch(error => {
          console.error('Failed to send service status change email:', error);
        });
      }
    }

    return NextResponse.json({
      success: true,
      service: updatedService,
    });

  } catch (error) {
    console.error("Error updating service status:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
