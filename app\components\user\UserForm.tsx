"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";

import { User } from "@/generated/prisma";
import { 
  UpdateUserFormValues, 
  updateUserSchema,
} from "@/app/zod/userSchemas";
import { updateUser } from "@/app/actions/userActions";

interface UserFormProps {
  user: User; // Now required, not optional
}

export default function UserForm({ user }: UserFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Set up form with default values
  const form = useForm<UpdateUserFormValues>({
    resolver: zodResolver(updateUserSchema),
    defaultValues: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      userAM: user.userAM || "",
      phoneNumber: user.phoneNumber || "",
      twoFactorEnabled: user.twoFactorEnabled,
      newpassword: "",
      confirmpassword: "",
      role: user.role,
      isActive: user.isActive,
      isSuspended: user.isSuspended,
      suspensionReason: user.suspensionReason || "",
      inactiveReason: user.inactiveReason || "",
      newsletterOptIn: user.newsletterOptIn,
      smsNotifications: user.smsNotifications,
      pushNotifications: user.pushNotifications,
      emailNotifications: user.emailNotifications
    }
  });
  
  // Handle form submission
  async function onSubmit(values: UpdateUserFormValues) {
    setIsSubmitting(true);
    
    try {
      
      // Update existing user
      const response = await updateUser({
        ...values
      });
      if(response.status==="SUCCESS"){
        toast.success("User updated successfully");
        router.push(`/users/${user.id}`);
      }else{
        toast.error(`${response.message}`)
      }
    } catch (error) {
      console.error("Error updating user:", error);
      toast.error("Failed to update user");
    } finally {
      setIsSubmitting(false);
    }
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        
        {/* Profile */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium">User Information</h3>
            <p className="text-sm text-muted-foreground">
              Update the users information in the system.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input 
                    type="email" 
                    placeholder="<EMAIL>" 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="+****************" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="userAM"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>User AM</FormLabel>
                  <FormControl>
                    <Input placeholder="AM12345" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
        </div>
        
        {/* Access */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium">Access</h3>
            <p className="text-sm text-muted-foreground">
              Enter the users access details.
            </p>
          </div>
          
          <div className="gap-4">
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="inregistratAB">User</SelectItem>
                      <SelectItem value="angajatAB">Employee</SelectItem>
                      <SelectItem value="moderatorAB">Moderator</SelectItem>
                      <SelectItem value="administAB">Administrator</SelectItem>
                      <SelectItem value="fourLvlInregistratAB">L4 User</SelectItem>
                      <SelectItem value="fourLvlAdminAB">L4 Admin</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Determines the users base permissions
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        
        {/* Security */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium">Security</h3>
            <p className="text-sm text-muted-foreground">
              Set the users security settings.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {user.passwordEnabled &&
              <>
                <FormField
                  control={form.control}
                  name="newpassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password</FormLabel>
                      <FormControl>
                        <Input placeholder="Rog setare parola complexa" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="confirmpassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <Input placeholder="Rog setare parola complexa" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            }

            <FormField
              control={form.control}
              name="isSuspended"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Suspend Status</FormLabel>
                    <FormDescription>
                      Determines if the user can log in to the system
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
              <FormField
                control={form.control}
                name="suspensionReason"
                render={({ field }) => (
                  <FormItem>
                    {/* <FormLabel>Suspension Reason</FormLabel> */}
                    <FormControl>
                      <Textarea 
                        placeholder="Suspension Reason description about the user" 
                        className="resize-none" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Active Status</FormLabel>
                  <FormDescription>
                    Determines if the user can log in to the system
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="inactiveReason"
            render={({ field }) => (
              <FormItem>
                {/* <FormLabel>Inactive Reason</FormLabel> */}
                <FormControl>
                  <Textarea 
                    placeholder="Inactive Reason description about the user" 
                    className="resize-none" 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          </div>
        </div>

        {/* Preferences */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium">Preferences</h3>
            <p className="text-sm text-muted-foreground">
              Set the users preferences and settings.
            </p>
          </div>
            <FormField
              control={form.control}
              name="emailNotifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Notificari prin email
                    </FormLabel>
                    <FormDescription>
                      Primiti notificari despre comenzi, actualizari de cont și alte informatii importante
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="pushNotifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Notificari push
                    </FormLabel>
                    <FormDescription>
                      Primiti notificari instant pe dispozitiv despre activitatea contului
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="smsNotifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Notificari SMS
                    </FormLabel>
                    <FormDescription>
                      Primiti SMS-uri pentru confirmari de comenzi și actualizari critice
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="newsletterOptIn"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Newsletter și oferte
                    </FormLabel>
                    <FormDescription>
                      Primiti newsletter-ul nostru cu oferte speciale, noutati și sfaturi utile
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
        </div>

        {/* Submit */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Update User"}
          </Button>
        </div>
      </form>
    </Form>
  );
}


