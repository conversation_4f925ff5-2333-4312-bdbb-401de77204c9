
import ProductDetails from "../components/product/ProductDetails";
import ProductAddAttributeCSV from "../components/attribute/ProductAddAttributeCSV";
import { ProductSearchForm } from "../components/product/ProductSearchForm";
import { getProductDetails } from "../getData/product/data";
import { ProductDetailsInterface } from "../types/Types";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import ProductAddPictures from "../components/attribute/ProductAddPicture";


interface SearchPageProps {
  searchParams: Promise<{
    materialNumber?: string; // The awaited object will have this structure
  }>;
}

export default async function ProductRoute(props: SearchPageProps) {
  await requireAdminOrModerator();
  const searchParams = await props.searchParams;
  const initialMaterialNumber = searchParams.materialNumber;

  let productDetails: ProductDetailsInterface | null = null;
  let serverErrors: string[] | undefined;

  if (initialMaterialNumber) {
    try {
      productDetails = await getProductDetails(initialMaterialNumber);
      if (!productDetails) {
        serverErrors = [`Product ${initialMaterialNumber} not found.`];
      }
    } catch (error) {
      serverErrors = [error instanceof Error ? error.message : 'An unexpected error occurred'];
    }
  }

  return (
      <div className="container mx-auto py-8 space-y-8">
        <h1 className="text-2xl font-bold">Product</h1>

        <ProductSearchForm 
          initialMaterialNumber={initialMaterialNumber} 
          serverErrors={serverErrors}
        />

        {productDetails && <ProductDetails foundProductDetails={productDetails} />}

        <ProductAddAttributeCSV />

        {productDetails && <ProductAddPictures materialNumber={productDetails.Material_Number} />}
      </div>
  );
}
