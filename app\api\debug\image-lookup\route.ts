import { NextRequest, NextResponse } from "next/server";
import { getImagesForMaterial, buildImageIndex } from "@/app/lib/imageCache";
import { logInfo } from "@/lib/logger";

/**
 * Debug API to help troubleshoot image lookup issues
 * Usage: GET /api/debug/image-lookup?materialNumber=ABC123
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const materialNumber = searchParams.get("materialNumber");

    if (!materialNumber) {
      return NextResponse.json(
        { error: "materialNumber parameter is required" },
        { status: 400 }
      );
    }

    // Get the full image index
    const imageIndex = await buildImageIndex();
    
    // Get images for the specific material number
    const images = await getImagesForMaterial(materialNumber);
    
    // Find similar material numbers (case variations, etc.)
    const allMaterialNumbers = Object.keys(imageIndex);
    const exactMatch = allMaterialNumbers.find(mn => mn === materialNumber);
    const caseInsensitiveMatches = allMaterialNumbers.filter(mn => 
      mn.toLowerCase() === materialNumber.toLowerCase() && mn !== materialNumber
    );
    const partialMatches = allMaterialNumbers.filter(mn => 
      mn.includes(materialNumber) || materialNumber.includes(mn)
    ).slice(0, 10); // Limit to 10 results

    // Log the lookup for debugging
    logInfo(`Debug image lookup for ${materialNumber}: found ${images.length} images`);

    return NextResponse.json({
      requestedMaterialNumber: materialNumber,
      exactMatch: exactMatch ? true : false,
      foundImages: images.length,
      images: images.map(img => ({
        fileName: img.fileName,
        materialNumber: img.materialNumber,
        sequenceNumber: img.sequenceNumber,
        fileExtension: img.fileExtension,
        fileSize: img.fileSize
      })),
      caseInsensitiveMatches,
      partialMatches: partialMatches.slice(0, 5), // Show first 5 partial matches
      indexStats: {
        totalMaterials: allMaterialNumbers.length,
        totalImages: Object.values(imageIndex).reduce((sum, imgs) => sum + imgs.length, 0)
      }
    });

  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
