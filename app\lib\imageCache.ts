'use cache'

import { promises as fs } from "fs";
import path from "path";
import { logError, logInfo } from "@/lib/logger";

// Get the pictures base path from environment
const PICTURES_BASE_PATH = process.env.PICTURES_BASE_PATH;

export interface ImageFile {
  fileName: string;
  materialNumber: string;
  sequenceNumber: number;
  fileExtension: string;
  fileSize: number;
  fullPath: string;
}

export interface ImageIndex {
  [materialNumber: string]: ImageFile[];
}

/**
 * Build the complete image index from the file system
 * This function is cached by Next.js and will only run once per server restart
 */
export async function buildImageIndex(): Promise<ImageIndex> {
  const startTime = Date.now();
  logInfo("Building image index from file system...");

  if (!PICTURES_BASE_PATH) {
    logError("PICTURES_BASE_PATH environment variable not configured");
    return {};
  }

  const imageIndex: ImageIndex = {};
  const supportedExtensions = [".jpg", ".jpeg", ".png", ".webp", ".gif"];

  try {
    // Check if directory exists
    try {
      await fs.access(PICTURES_BASE_PATH);
    } catch {
      logError(`Pictures directory not accessible: ${PICTURES_BASE_PATH}`);
      return {};
    }

    // Read all files from the pictures directory
    const files = await fs.readdir(PICTURES_BASE_PATH);
    let validImages = 0;

    for (const fileName of files) {
      try {
        const fileExtension = path.extname(fileName).toLowerCase();
        
        // Skip non-image files
        if (!supportedExtensions.includes(fileExtension)) {
          continue;
        }

        // Parse material number and sequence from filename
        const { materialNumber, sequenceNumber } = parseImageFileName(fileName);
        
        if (!materialNumber) {
          continue; // Skip files that don't match our naming pattern
        }

        // Get file stats
        const fullPath = path.join(PICTURES_BASE_PATH, fileName);
        const stats = await fs.stat(fullPath);

        const imageFile: ImageFile = {
          fileName,
          materialNumber,
          sequenceNumber,
          fileExtension,
          fileSize: stats.size,
          fullPath
        };

        // Add to index
        if (!imageIndex[materialNumber]) {
          imageIndex[materialNumber] = [];
        }
        imageIndex[materialNumber].push(imageFile);
        
        validImages++;
      } catch (error) {
        logError(`Error processing file ${fileName}:`, error);
      }
    }

    // Sort images by sequence number for each material
    Object.keys(imageIndex).forEach(materialNumber => {
      imageIndex[materialNumber].sort((a, b) => a.sequenceNumber - b.sequenceNumber);
    });

    const endTime = Date.now();
    const duration = endTime - startTime;
    const materialCount = Object.keys(imageIndex).length;

    logInfo(`Image index built successfully: ${validImages} images for ${materialCount} materials in ${duration}ms`);

    return imageIndex;

  } catch (error) {
    logError("Failed to build image index:", error);
    return {};
  }
}

/**
 * Get images for a specific material number from the cached index
 */
export async function getImagesForMaterial(materialNumber: string): Promise<ImageFile[]> {
  const imageIndex = await buildImageIndex();
  return imageIndex[materialNumber] || [];
}

/**
 * Get images for multiple material numbers from the cached index
 */
export async function getImagesForMaterials(materialNumbers: string[]): Promise<{ [materialNumber: string]: ImageFile[] }> {
  const imageIndex = await buildImageIndex();
  const result: { [materialNumber: string]: ImageFile[] } = {};
  
  for (const materialNumber of materialNumbers) {
    result[materialNumber] = imageIndex[materialNumber] || [];
  }
  
  return result;
}

/**
 * Get statistics about the image index
 */
export async function getImageIndexStats(): Promise<{
  totalMaterials: number;
  totalImages: number;
  averageImagesPerMaterial: number;
  topMaterialsWithMostImages: Array<{ materialNumber: string; imageCount: number }>;
}> {
  const imageIndex = await buildImageIndex();
  const materialNumbers = Object.keys(imageIndex);
  const totalMaterials = materialNumbers.length;
  const totalImages = materialNumbers.reduce((sum, material) => sum + imageIndex[material].length, 0);
  const averageImagesPerMaterial = totalMaterials > 0 ? totalImages / totalMaterials : 0;
  
  // Get top 10 materials with most images
  const topMaterialsWithMostImages = materialNumbers
    .map(materialNumber => ({
      materialNumber,
      imageCount: imageIndex[materialNumber].length
    }))
    .sort((a, b) => b.imageCount - a.imageCount)
    .slice(0, 10);

  return {
    totalMaterials,
    totalImages,
    averageImagesPerMaterial: Math.round(averageImagesPerMaterial * 100) / 100,
    topMaterialsWithMostImages
  };
}

/**
 * Parse material number and sequence from filename
 * Supports patterns: {materialNumber}.ext and {materialNumber}_N.ext
 */
function parseImageFileName(fileName: string): { materialNumber: string | null; sequenceNumber: number } {
  const nameWithoutExt = path.parse(fileName).name;
  
  // Check for sequenced pattern: {materialNumber}_N
  const sequencedMatch = nameWithoutExt.match(/^(.+)_(\d+)$/);
  if (sequencedMatch) {
    return {
      materialNumber: sequencedMatch[1],
      sequenceNumber: parseInt(sequencedMatch[2], 10)
    };
  }
  
  // Base pattern: {materialNumber}
  return {
    materialNumber: nameWithoutExt,
    sequenceNumber: 0
  };
}

/**
 * Force rebuild the image index (useful for development or when files change)
 * Note: In production, you would restart the server to rebuild the cache
 */
export async function rebuildImageIndex(): Promise<ImageIndex> {
  // This would typically require cache invalidation in a real implementation
  // For now, we'll just rebuild (cache will still be used until server restart)
  return await buildImageIndex();
}
