import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Pencil } from "lucide-react";
import { getBannerById } from "@/app/getData/banner/getBanners";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import Image from "next/image";

export const generateMetadata = async ({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> => {
  const paramsObject = await params;
  const id = paramsObject.id;
  const banner = await getBannerById(id);
  return {
    title: banner ? `Banner: ${banner.title}` : "Banner Not Found",
    description: banner?.description || "Banner details",
  };
};

export default async function BannerDetailPage({ params }: { params: Promise<{ id: string } > }) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const idAsString = paramsObject.id;    //All params are strings and need to be parsed
  const banner = await getBannerById(idAsString);
  
  if (!banner) {
    notFound();
  }
  
  const formatDate = (date: Date | null) => {
    if (!date) return "No end date";
    return new Date(date).toLocaleString();
  };
  
  const isActive = banner.isActive && 
    (new Date() >= new Date(banner.startDate)) && 
    (!banner.endDate || new Date() <= new Date(banner.endDate));
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{banner.title}</h1>
        <div className="flex gap-4">
          <Link href="/banner">
            <Button variant="outline">Back to Banners</Button>
          </Link>
          <Link href={`/banner/edit/${banner.id}`}>
            <Button>
              <Pencil className="mr-2 h-4 w-4" />
              Edit Banner
            </Button>
          </Link>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Banner Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md overflow-hidden">
                <Image
                  src={`/api/images/${banner.imageUrl}`}
                  alt={banner.title}
                  className="w-full h-auto"
                  width={800}
                  height={600}
                />
              </div>

              {banner.mobileImageUrl && (
                <div className="mt-4">
                  <h3 className="text-lg font-medium mb-2">Mobile Preview</h3>
                  <div className="border rounded-md overflow-hidden max-w-xs">
                    <Image
                      src={`/api/images/${banner.mobileImageUrl}`}
                      alt={`${banner.title} (Mobile)`}
                      className="w-full h-auto"
                      width={800}
                      height={600}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {banner.subtitle && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Subtitle</h3>
                  <p>{banner.subtitle}</p>
                </div>
              )}
              
              {banner.description && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Description</h3>
                  <p>{banner.description}</p>
                </div>
              )}
              
              {banner.callToAction && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Call to Action</h3>
                  <p>{banner.callToAction}</p>
                </div>
              )}
              
              {banner.buttonText && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Button Text</h3>
                  <p>{banner.buttonText}</p>
                </div>
              )}
              
              {banner.url && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">URL</h3>
                  <a href={banner.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                    {banner.url}
                  </a>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Banner Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Status</h3>
                <Badge variant={isActive ? "default" : "secondary"}>
                  {isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Placement</h3>
                <p>{banner.placement}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Position</h3>
                <p>{banner.position}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Text Alignment</h3>
                <p>{banner.textAlignment}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Device Target</h3>
                <p>{banner.deviceTarget}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
                <p>{formatDate(banner.startDate)}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">End Date</h3>
                <p>{formatDate(banner.endDate)}</p>
              </div>
              
              {(banner.width || banner.height) && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Dimensions</h3>
                  <p>{banner.width || "Auto"} × {banner.height || "Auto"}</p>
                </div>
              )}
              
              {(banner.backgroundColor || banner.textColor) && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Colors</h3>
                  {banner.backgroundColor && (
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-4 h-4 rounded-full border" 
                        style={{ backgroundColor: banner.backgroundColor }}
                      />
                      <span>Background: {banner.backgroundColor}</span>
                    </div>
                  )}
                  {banner.textColor && (
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-4 h-4 rounded-full border" 
                        style={{ backgroundColor: banner.textColor }}
                      />
                      <span>Text: {banner.textColor}</span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Impressions</h3>
                <p>{banner.impressions.toLocaleString()}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Clicks</h3>
                <p>{banner.clicks.toLocaleString()}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Conversion Rate</h3>
                <p>
                  {banner.conversionRate !== null 
                    ? `${banner.conversionRate.toFixed(2)}%` 
                    : 'N/A'}
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Created By</h3>
                <p>{banner.createdBy || "System"}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Last Updated By</h3>
                <p>{banner.updatedBy || "System"}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Created At</h3>
                <p>{new Date(banner.createdAt).toLocaleString()}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                <p>{new Date(banner.updatedAt).toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
