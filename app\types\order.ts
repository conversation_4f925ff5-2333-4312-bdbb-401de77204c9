import { Decimal } from "@prisma/client/runtime/library";
import type {  $Enums } from "@/generated/prisma";

export interface User {
  id?: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
}

export interface Address {
  id: string;
  fullName: string;
  companyName?: string | null;
  addressLine1?: string;
  addressLine2?: string | null;
  city: string;
  state?: string;
  county?: string;
  postalCode?: string;
  country?: string;
  phone?: string | null;
}

export interface Product {
  id: string;
  name?: string;
  Material_Number?: string;
  Material_Group?: string | null;
  PretAM?: Decimal | null;
  FinalPrice?: Decimal | null;
}

export interface OrderItem {
  id: string;
  quantity: number;
  unitPrice?: number;
  price: Decimal;
  notes?: string | null;
  product?: Product;
  productId: string;
}

export interface OrderStatusHistory {
  id: string;
  status: $Enums.OrderStatus;
  note?: string | null;
  updatedBy?: string | null;
  createdAt: Date;
}

export interface Order {
  id: string;
  orderNumber: string;
  amount: Decimal;
  totalAmount: Decimal;
  isPaid: boolean;
  invoiceAM?: string | null;
  invoiceFilePath?: string | null;
  orderStatus: string;
  paymentMethod: string;
  paymentStatus: string;
  shippingMethod: string;
  shipmentStatus: string;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
  user?: User;
  userId: string;
  billingAddress?: Address;
  shippingAddress?: Address;
  orderItems: OrderItem[];
  statusHistory: OrderStatusHistory[];
}

export interface RawOrders {
    user: {
      email: string;
      firstName: string;
      lastName: string;
    };
    id: string,
    orderNumber: string,
    amount: Decimal,
    isPaid: boolean,
    totalAmount: Decimal,
    orderStatus: $Enums.OrderStatus,
    paymentStatus: $Enums.PaymentStatus,
    createdAt: Date,
    notes: string | null,
}

export interface ReturnOrders {
    user: {
      email: string;
      firstName: string;
      lastName: string;
    };
    id: string,
    orderNumber: string,
    amount: number,
    isPaid: boolean,
    orderStatus: $Enums.OrderStatus,
    paymentStatus: $Enums.PaymentStatus,
    createdAt: string,
    notes: string | null,
    totalAmount: number,
}