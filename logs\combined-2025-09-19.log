{"level":"info","message":"Order status change email sent:","messageId":"<<EMAIL>>","orderId":"cmfqfwwww000khx487h9p60kz","orderNumber":"ORD-2025-30513","statusChange":"plasata -> completa","timestamp":"2025-09-19 09:14:12","to":"<EMAIL>"}
{"level":"info","message":"uploadInvoiceAction -> Successfully uploaded invoice for order ORD-2025-30513 to \\\\VSRV-OFC-FS-BAN\\Work\\IT\\site-piese_invoices_pics\\invoices\\161616.<NAME_EMAIL>","timestamp":"2025-09-19 09:14:38"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:15:40"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:16:05"}
{"level":"info","message":"Return cmfpjbbxa000ahx48o7djkgg8 status updated from refundIssued to <NAME_EMAIL>","timestamp":"2025-09-19 09:18:13"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:18:22"}
{"level":"info","message":"Return cmfpjbbxa000ahx48o7djkgg8 status updated from approved to <NAME_EMAIL>","timestamp":"2025-09-19 09:18:23"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:18:41"}
{"level":"info","message":"Return cmfpjbbxa000ahx48o7djkgg8 status updated from requested to <NAME_EMAIL>","timestamp":"2025-09-19 09:18:52"}
{"level":"error","message":"Error updating return status: PrismaClientValidationError: \nInvalid `prisma.return.findUnique()` invocation:\n\n{\n  where: {\n    id: \"cmfpjbbxa000ahx48o7djkgg8\"\n  },\n  include: {\n    user: {\n    ~~~~\n      select: {\n        firstName: true,\n        lastName: true,\n        email: true\n      }\n    },\n    order: {\n      select: {\n        orderNumber: true\n      }\n    },\n    returnItems: {\n      include: {\n        orderItem: {\n          include: {\n            product: {\n              select: {\n                Description_Local: true,\n                Material_Number: true\n              }\n            }\n          }\n        }\n      }\n    },\n?   address?: true,\n?   showroom?: true,\n?   statusHistory?: true,\n?   action?: true\n  }\n}\n\nUnknown field `user` for include statement on model `Return`. Available options are marked with ?. by <EMAIL>","timestamp":"2025-09-19 09:27:23"}
{"level":"info","message":"Service status change email sent:","messageId":"<<EMAIL>>","serviceNumber":"SVC-2025-00002","statusChange":"scheduled -> inProgress","timestamp":"2025-09-19 09:32:17","to":"<EMAIL>"}
{"level":"info","message":"Service status change email sent:","messageId":"<<EMAIL>>","serviceNumber":"SVC-2025-00002","statusChange":"inProgress -> diagnosisComplete","timestamp":"2025-09-19 09:32:40","to":"<EMAIL>"}
{"level":"info","message":"Service status change email sent:","messageId":"<<EMAIL>>","serviceNumber":"SVC-2025-00002","statusChange":"diagnosisComplete -> awaitingParts","timestamp":"2025-09-19 09:33:23","to":"<EMAIL>"}
{"level":"info","message":"Service status change email sent:","messageId":"<<EMAIL>>","serviceNumber":"SVC-2025-00002","statusChange":"awaitingParts -> awaitingApproval","timestamp":"2025-09-19 09:34:19","to":"<EMAIL>"}
{"level":"info","message":"Service status change email sent:","messageId":"<<EMAIL>>","serviceNumber":"SVC-2025-00002","statusChange":"awaitingApproval -> completed","timestamp":"2025-09-19 09:35:41","to":"<EMAIL>"}
{"level":"info","message":"Return cmfpjbbxa000ahx48o7djkgg8 status updated from inspected to <NAME_EMAIL>","timestamp":"2025-09-19 09:39:37"}
{"level":"info","message":"Return status change email sent:","messageId":"<<EMAIL>>","returnNumber":"RET-2025-440901","statusChange":"inspected -> rejected","timestamp":"2025-09-19 09:39:39","to":"<EMAIL>"}
{"level":"info","message":"Return cmfqgwlvh000zhx48qx8io7a4 status updated from requested to <NAME_EMAIL>","timestamp":"2025-09-19 09:40:31"}
{"level":"info","message":"Return status change email sent:","messageId":"<<EMAIL>>","returnNumber":"RET-2025-860881","statusChange":"requested -> approved","timestamp":"2025-09-19 09:40:33","to":"<EMAIL>"}
{"level":"info","message":"Return cmfqgwlvh000zhx48qx8io7a4 status updated from approved to <NAME_EMAIL>","timestamp":"2025-09-19 09:41:31"}
{"level":"info","message":"Return status change email sent:","messageId":"<<EMAIL>>","returnNumber":"RET-2025-860881","statusChange":"approved -> awaitingReceipt","timestamp":"2025-09-19 09:41:32","to":"<EMAIL>"}
{"level":"info","message":"Return cmfqgwlvh000zhx48qx8io7a4 status updated from awaitingReceipt to <NAME_EMAIL>","timestamp":"2025-09-19 09:41:41"}
{"level":"info","message":"Return status change email sent:","messageId":"<<EMAIL>>","returnNumber":"RET-2025-860881","statusChange":"awaitingReceipt -> received","timestamp":"2025-09-19 09:41:42","to":"<EMAIL>"}
{"level":"info","message":"Return cmfqgwlvh000zhx48qx8io7a4 status updated from received to <NAME_EMAIL>","timestamp":"2025-09-19 09:42:10"}
{"level":"info","message":"Return status change email sent:","messageId":"<<EMAIL>>","returnNumber":"RET-2025-860881","statusChange":"received -> inspected","timestamp":"2025-09-19 09:42:11","to":"<EMAIL>"}
{"level":"info","message":"Return cmfqgwlvh000zhx48qx8io7a4 status updated from inspected to <NAME_EMAIL>","timestamp":"2025-09-19 09:42:42"}
{"level":"info","message":"Return status change email sent:","messageId":"<<EMAIL>>","returnNumber":"RET-2025-860881","statusChange":"inspected -> refundIssued","timestamp":"2025-09-19 09:42:43","to":"<EMAIL>"}
{"level":"info","message":"Return cmfqgwlvh000zhx48qx8io7a4 status updated from refundIssued to <NAME_EMAIL>","timestamp":"2025-09-19 09:42:59"}
{"level":"info","message":"Return status change email sent:","messageId":"<<EMAIL>>","returnNumber":"RET-2025-860881","statusChange":"refundIssued -> completed","timestamp":"2025-09-19 09:43:00","to":"<EMAIL>"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-19 09:43:39"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-19 09:43:45"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-19 09:44:29"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 88127ms","timestamp":"2025-09-19 09:45:07"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 51629811625, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 51622166959, found 3 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 07119915703, found 0 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 83122298222, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 51498270015, found 2 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 51627441458, found 1 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 07500018229, found 0 images","timestamp":"2025-09-19 09:45:08"}
{"level":"info","message":"Image API -> Looking for material 83122285677, found 1 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 51622161763, found 2 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 12317501755, found 0 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 61126927572, found 1 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 36115A24E75, found 3 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 72608370321, found 1 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 36112462643, found 3 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Looking for material 51470427563, found 1 images","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 51629811625.jpg for 51629811625","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 51622166959.jpg for 51622166959","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 83122298222.jpg for 83122298222","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 51498270015.jpg for 51498270015","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 51627441458.jpg for 51627441458","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 83122285677.jpg for 83122285677","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 51622161763.jpg for 51622161763","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 61126927572_2.jpg for 61126927572","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 72608370321_2.jpg for 72608370321","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 36112462643.jpg for 36112462643","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served 51470427563_2.jpg for 51470427563","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-19 09:45:09"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-19 10:17:32"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-19 10:17:32"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 95396ms","timestamp":"2025-09-19 10:19:07"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material bmw_logo_M, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material performance, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material ingrijire, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material parts, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material roti, found 1 images","timestamp":"2025-09-19 10:19:08"}
{"level":"info","message":"Image API -> Looking for material 51622166959, found 3 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 51627441458, found 1 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 51629811625, found 1 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 51622161763, found 2 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 83122285677, found 1 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 83122298222, found 1 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 72608370321, found 1 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 64119382609, found 2 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 51498270015, found 2 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 36115A24E75, found 3 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 36112462643, found 3 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 51470427563, found 1 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Looking for material 61126927572, found 1 images","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served bmw_logo_M.webp for bmw_logo_M","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served performance.jpg for performance","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served parts.jpg for parts","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served ingrijire.jpg for ingrijire","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served roti.jpg for roti","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 51627441458.jpg for 51627441458","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 51622166959.jpg for 51622166959","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 51629811625.jpg for 51629811625","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 83122298222.jpg for 83122298222","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 83122285677.jpg for 83122285677","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 72608370321_2.jpg for 72608370321","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 51622161763.jpg for 51622161763","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 36115A24E75.jpg for 36115A24E75","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 51498270015.jpg for 51498270015","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 36112462643.jpg for 36112462643","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 64119382609.jpg for 64119382609","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 61126927572_2.jpg for 61126927572","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served 51470427563_2.jpg for 51470427563","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-19 10:19:09"}
{"level":"info","message":"Building image index from file system...","timestamp":"2025-09-19 12:18:36"}
{"level":"info","message":"Image index built successfully: 10334 images for 8799 materials in 84895ms","timestamp":"2025-09-19 12:20:01"}
{"level":"info","message":"Image API -> Looking for material bmw3csl, found 1 images","timestamp":"2025-09-19 12:20:02"}
{"level":"info","message":"Image API -> Looking for material default, found 2 images","timestamp":"2025-09-19 12:20:02"}
{"level":"info","message":"Image API -> Looking for material fortza, found 1 images","timestamp":"2025-09-19 12:20:02"}
{"level":"info","message":"Image API -> Looking for material acceories, found 1 images","timestamp":"2025-09-19 12:20:02"}
{"level":"info","message":"Image API -> Served fortza.jpg for fortza","timestamp":"2025-09-19 12:20:02"}
{"level":"info","message":"Image API -> Served default.png for default","timestamp":"2025-09-19 12:20:02"}
{"level":"info","message":"Image API -> Served acceories.jpg for acceories","timestamp":"2025-09-19 12:20:02"}
{"level":"info","message":"Image API -> Served bmw3csl.png for bmw3csl","timestamp":"2025-09-19 12:20:02"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /banner couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /classes couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /dashboard couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route / couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /discounts couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /product couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /profile couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /orders couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /returns couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /services couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /unauthorized couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:18"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /settings couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /sign-out couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /categories couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /users couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /discounts/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /_not-found couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /featured couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /banner/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /groups/create couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /analytics couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
{"level":"error","message":"Error fetching current user: {\"description\":\"Route /groups couldn't be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error\",\"digest\":\"DYNAMIC_SERVER_USAGE\"}","timestamp":"2025-09-19 12:23:19"}
